#!/usr/bin/env python3
"""
测试编号列表取消首行缩进效果
"""

import asyncio
import os
import sys

# 添加backend目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.pdf_service import PDFService
from app.models.schemas import LayoutConfig

async def test_no_indent():
    """测试编号列表取消首行缩进效果"""
    
    # 创建PDF服务实例
    pdf_service = PDFService()
    
    # 测试内容 - 重点对比首行缩进效果
    test_content = """# 编号列表首行缩进测试

## 修复说明

编号列表中序号后的文字不应该有首行缩进，现在已经设置 `firstLineIndent=0`。

## 普通段落（有首行缩进）

这是普通段落的文字，应该有首行缩进效果。这个段落用来对比编号列表的效果。

另一个普通段落，同样应该有首行缩进。可以看到段落开头有明显的缩进空间。

## 编号列表（无首行缩进）

1. 第一个列表项，文字应该紧贴序号圆形，没有额外的首行缩进
2. 第二个列表项包含（（楷体双括号内容））测试
3. 第三个列表项：**粗体**、（（楷体））、*斜体*混合格式
4. 第四个列表项有较长的文字内容，用来测试在长文本情况下是否仍然没有首行缩进效果

## 对比验证

### 普通段落效果
这个段落应该有首行缩进，文字开头会有一定的空白距离。

这是另一个普通段落，同样有首行缩进效果。

### 编号列表效果
1. 这个列表项应该没有首行缩进，文字直接从序号后开始
2. 另一个列表项（（楷体强调内容））也没有首行缩进
3. 长文本列表项：这是一个比较长的列表项内容，用来验证在文字较多的情况下，是否仍然保持没有首行缩进的效果

## 实际应用测试

### 数学题目
1. 计算下列各题：（（注意运算顺序））
2. 解方程：x + 5 = 12，求x的值
3. 应用题：小明有苹果若干个，给了小红3个后还剩（（关键数字））个

### 语文阅读
1. 阅读理解：仔细阅读文章，理解（（中心思想））
2. 词语解释：解释下列词语的含义，注意（（重点词汇））
3. 写作练习：按要求写作，注意（（写作技巧））的运用

### 英语练习
1. Grammar exercise: Complete the sentences with correct（（grammar forms））
2. Vocabulary practice: Learn and remember these（（key words））
3. Speaking drill: Practice pronunciation of（（difficult sounds））

## 长文本测试

1. 这是一个很长很长的列表项内容，包含了大量的文字信息，用来测试在文本内容非常多的情况下，编号列表项是否仍然能够保持没有首行缩进的效果，同时验证文字的换行和对齐是否正确
2. 另一个长文本项目（（包含楷体双括号强调内容用来测试复杂格式在长文本中的显示效果））
3. 最后一个测试项目包含多种格式组合：**粗体文字**、（（橙色楷体文字））、*斜体文字*、`代码文字`的混合使用效果

## 验证要点

如果修复成功，应该看到：
- 普通段落有首行缩进
- 编号列表项没有首行缩进
- 序号与文字间距合适（8px）
- 双括号楷体正常显示

（（首行缩进修复完成，编号列表排版更加规范！））
"""
    
    # 配置 - 确保启用首行缩进以便对比
    config = LayoutConfig(
        page_format="A4",
        margin_top=2.0,
        margin_bottom=2.0,
        margin_left=2.0,
        margin_right=2.0,
        font_family="Noto Sans CJK SC",
        font_size=12,
        line_height=1.5,
        paragraph_spacing=6,
        indent_first_line=True,  # 启用首行缩进以便对比
        dpi=300,
        color_mode="CMYK",
        bleed=3,
        enable_hyphenation=True,
        widow_orphan_control=True
    )
    
    try:
        # 生成PDF
        pdf_path = await pdf_service.generate_pdf(
            test_content, 
            config, 
            "no_indent_test.pdf"
        )
        
        print(f"✅ PDF生成成功: {pdf_path}")
        print(f"📄 文件位置: {os.path.abspath(pdf_path)}")
        
        # 检查文件是否存在
        if os.path.exists(pdf_path):
            file_size = os.path.getsize(pdf_path)
            print(f"📊 文件大小: {file_size} 字节")
            
            print("\n📝 首行缩进设置:")
            print("   📄 普通段落：有首行缩进（20px）")
            print("   📋 编号列表：无首行缩进（0px）")
            print("   📐 序号间距：8px")
            print("   🎯 对比效果：明显区别")
            
            print("\n🔧 技术实现:")
            print("   ✅ 设置 firstLineIndent=0")
            print("   ✅ 覆盖父样式的缩进设置")
            print("   ✅ 保持其他格式不变")
            print("   ✅ 双括号楷体正常显示")
            
        else:
            print("❌ PDF文件未找到")
            
    except Exception as e:
        print(f"❌ 生成PDF时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_no_indent())
